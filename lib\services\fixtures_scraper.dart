import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart';
import 'package:cat_tv/models/fixture.dart';
import 'package:puppeteer/puppeteer.dart'; // Import Puppeteer
import 'package:cat_tv/utils/ad_blocker.dart';

class FixturesScraper {
  static const String _baseUrl = 'https://www.livesoccertv.com';
  static const String _schedulesUrl = '$_baseUrl/schedules/';
  static const String _cacheFilePath = 'cache/fixtures_data.json';
  static const String _lastUpdateFilePath = 'cache/last_fixtures_update.txt';

  static Future<bool> shouldUpdateFixtures() async {
    try {
      final lastUpdateFile = File(_lastUpdateFilePath);
      if (!await lastUpdateFile.exists()) {
        return true; // No previous update, should update
      }

      final lastUpdateStr = await lastUpdateFile.readAsString();
      final lastUpdate = DateTime.tryParse(lastUpdateStr);
      if (lastUpdate == null) {
        return true; // Invalid date, should update
      }

      final now = DateTime.now();
      final difference = now.difference(lastUpdate);

      // Update if more than 24 hours have passed
      return difference.inHours >= 24;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking last update: $e');
      }
      return true; // Error occurred, should update
    }
  }

  static Future<List<FixtureLeague>> scrapeFixtures() async {
    Browser? browser;
    try {
      if (kDebugMode) {
        print('Launching browser for scraping...');
      }
      browser = await puppeteer.launch(
        headless: false, // Set to true for production, false for debugging
      );
      final page = await browser.newPage();
      await page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      );

      await page.setRequestInterception(true);
      page.onRequest.listen((request) {
        if (AdBlocker.isBlocked(request.url)) {
          request.abort(); // Block the request
        } else {
          request.continueRequest();
        }
      });

      if (kDebugMode) {
        print('Navigating to: $_schedulesUrl');
      }
      await AdBlocker.loadEasylist();
      final content = await _fetchWithRetry(page, _schedulesUrl);

      // Attempt to accept the privacy notice if it appears
      try {
        if (kDebugMode) {
          print('Waiting for #snigel-cmp-framework element...');
        }
        await page.waitForSelector(
          '#snigel-cmp-framework',
          timeout: Duration(seconds: 10),
        );
        if (kDebugMode) {
          print(
            'Privacy notice element found. Attempting to click "Accept all" button.',
          );
        }
        // Click the "Accept all & visit the site" button by its ID
        await page.click('#accept-choices');
        await Future.delayed(const Duration(seconds: 2)); // 2-second delay
        if (kDebugMode) {
          print('Clicked privacy notice "Accept all" button.');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Privacy notice element not found or error clicking: $e');
        }
      }

      if (kDebugMode) {
        print('started scraping');
      }

      final document = html_parser.parse(content);
      final leagues = <FixtureLeague>[];

      // Parse schedules table
      final parsedData = _parseSchedulesTable(document);
      leagues.addAll(parsedData);

      if (kDebugMode) {
        print('Scraped ${leagues.length} leagues with matches');
      }
      return leagues;
    } catch (e) {
      if (kDebugMode) {
        print('Error during Puppeteer scraping: $e');
      }
      rethrow;
    } finally {
      if (browser != null) {
        if (kDebugMode) {
          print('Closing browser.');
        }
        await browser.close();
      }
    }
  }

  // Helper method for fetching with retry and rate limiting
  static Future<String> _fetchWithRetry(
    Page page,
    String url, {
    int retries = 1, // Changed to 1 attempt as requested
    Duration delay = const Duration(seconds: 2),
  }) async {
    for (int i = 0; i < retries; i++) {
      try {
        await page.goto(
          url,
          wait: Until.networkIdle,
          timeout: const Duration(seconds: 30),
        );
        final content = await page.content ?? '';
        if (content.isNotEmpty) {
          return content;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Attempt ${i + 1} failed for $url: $e');
        }
        if (i < retries - 1) {
          // Exponential backoff
          await Future.delayed(delay * (i + 1));
        }
      }
    }
    throw Exception('Failed to fetch $url after $retries attempts.');
  }

  static List<FixtureLeague> _parseSchedulesTable(Document document) {
    final parsedData = <FixtureLeague>[];
    if (kDebugMode) {
      print('\n--- Starting schedules table parsing ---');
    }

    // Find the main element
    final mainElement = document.querySelector('main');
    if (mainElement == null) {
      if (kDebugMode) {
        print('Could not find the <main> element.');
      }
      return parsedData;
    }

    // Find the schedules table
    final schedulesTable = mainElement.querySelector('table.schedules');
    if (schedulesTable == null) {
      if (kDebugMode) {
        print('Could not find the schedules table with class "schedules".');
      }
      return parsedData;
    }

    if (kDebugMode) {
      print('--- Found schedules table. ---');
    }
    final tbody = schedulesTable.querySelector('tbody');
    if (tbody == null) {
      if (kDebugMode) {
        print('Could not find the <tbody> element within the schedules table.');
      }
      return parsedData;
    }

    if (kDebugMode) {
      print('--- Found tbody. Parsing rows... ---');
    }
    FixtureLeague? currentLeague;

    // Get all direct tr children
    final rows = tbody.querySelectorAll('tr');

    for (final row in rows) {
      final classes = row.classes;

      if (classes.contains('sortable_comp')) {
        // This is a league header row
        final league = _parseLeagueRow(row);
        if (league != null) {
          currentLeague = league;
          parsedData.add(currentLeague);
        }
      } else if (classes.contains('matchrow')) {
        // This is a match row
        final matchId = row.attributes['id'];
        if (kDebugMode) {
          print('  Processing Match Row (ID: $matchId)');
        }

        if (currentLeague != null) {
          final match = _parseMatchRow(row);
          if (match != null) {
            currentLeague.matches.add(match);
            if (kDebugMode) {
              print('    Added match to current league: ${match.teams}');
            }
          }
        } else {
          if (kDebugMode) {
            print(
              '    Warning: Found match row without a preceding league header. Skipping.',
            );
          }
        }
      }
    }

    if (kDebugMode) {
      print('\n--- Finished schedules table parsing ---');
    }
    return parsedData;
  }

  static FixtureLeague? _parseLeagueRow(Element row) {
    try {
      // Look for league header row with class 'sortable_comp'
      if (!row.classes.contains('sortable_comp')) {
        return null;
      }

      final compRowTd = row.querySelector('td.r_comprow');
      if (compRowTd == null) return null;

      // Get league name from span with class 'flag'
      final leagueNameSpan = compRowTd.querySelector('span.flag');
      if (leagueNameSpan == null) return null;

      final leagueName = leagueNameSpan.text.trim();
      if (leagueName.isEmpty) return null;

      // Get flag class (all classes except 'flag')
      final flagClasses =
          leagueNameSpan.classes.where((c) => c != 'flag').toList();
      final flagClass = flagClasses.isNotEmpty ? flagClasses.first : 'world';

      // Get league URL from a tag with class 'flag eurl'
      final leagueUrlA = compRowTd.querySelector('a.flag.eurl');
      final leagueUrl = leagueUrlA?.attributes['href'] ?? '';

      if (kDebugMode) {
        print('  Parsed League: $leagueName');
      }
      if (kDebugMode) {
        print('  League URL: $leagueUrl');
      }

      return FixtureLeague(
        leagueName: leagueName,
        flagClass: flagClass,
        leagueUrl: leagueUrl,
        matches: [], // Matches will be added separately
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing league row: $e');
      }
      return null;
    }
  }

  static FixtureMatch? _parseMatchRow(Element row) {
    try {
      final matchId = row.attributes['id'] ?? '';

      // Get match time from timecol
      final timeCol = row.querySelector('td.timecol');
      final timeSpan = timeCol?.querySelector('span.ts');
      final matchTime = timeSpan?.text.trim() ?? 'N/A';
      if (kDebugMode) {
        print('    Match Time: $matchTime');
      }

      // Get match details from match td
      final matchTd = row.querySelector('td#match');
      final matchLink = matchTd?.querySelector('a');

      String matchTitle = 'N/A';
      String matchTeams = 'N/A';
      String matchUrl = 'N/A';

      if (matchLink != null) {
        if (kDebugMode) {
          print('    Match Link found: ${matchLink.outerHtml}');
        }
        matchTitle = matchLink.attributes['title']?.trim() ?? 'N/A';
        matchTeams = matchLink.text.trim();
        matchUrl = matchLink.attributes['href'] ?? 'N/A';
      } else {
        if (kDebugMode) {
          print('    Match Link not found.');
        }
      }

      if (kDebugMode) {
        print('    Match Title: $matchTitle');
        print('    Match Teams: $matchTeams');
        print('    Match URL: $matchUrl');
      }

      // Get channels from channels td
      final channelsTd = row.querySelector('td#channels');
      final channelsDiv = channelsTd?.querySelector('div.mchannels');
      final channels = <String>[];

      if (channelsDiv != null) {
        final channelLinks = channelsDiv.querySelectorAll('a');
        for (final channelLink in channelLinks) {
          final channelName = channelLink.attributes['title'];
          if (channelName != null && channelName.trim().isNotEmpty) {
            channels.add(channelName.trim());
          }
        }
      }
      if (kDebugMode) {
        print('    Channels: $channels');
      }

      return FixtureMatch(
        matchId: matchId,
        time: matchTime,
        title: matchTitle,
        teams: matchTeams,
        url: matchUrl,
        channels: channels,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing match row: $e');
      }
      return null;
    }
  }

  static Future<void> saveFixturesToCache(List<FixtureLeague> leagues) async {
    try {
      // Ensure cache directory exists
      final cacheDir = Directory('cache');
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      // Convert to JSON
      final jsonData =
          leagues
              .map(
                (league) => {
                  'league_name': league.leagueName,
                  'flag_class': league.flagClass,
                  'league_url': league.leagueUrl,
                  'matches':
                      league.matches
                          .map(
                            (match) => {
                              'match_id': match.matchId,
                              'time': match.time,
                              'title': match.title,
                              'teams': match.teams,
                              'url': match.url,
                              'channels': match.channels,
                            },
                          )
                          .toList(),
                },
              )
              .toList();

      // Save to file
      final file = File(_cacheFilePath);
      await file.writeAsString(json.encode(jsonData));

      // Update last update timestamp
      final lastUpdateFile = File(_lastUpdateFilePath);
      await lastUpdateFile.writeAsString(DateTime.now().toIso8601String());

      if (kDebugMode) {
        print('Fixtures saved to cache: ${leagues.length} leagues');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving fixtures to cache: $e');
      }
      rethrow;
    }
  }

  static Future<List<FixtureLeague>> updateFixturesIfNeeded() async {
    try {
      if (await shouldUpdateFixtures()) {
        if (kDebugMode) {
          print('Updating fixtures...');
        }
        try {
          final leagues = await scrapeFixtures();
          await saveFixturesToCache(leagues);
          return leagues;
        } catch (e) {
          if (kDebugMode) {
            print('Scraping failed, creating sample data: $e');
          }
          // If scraping fails, create some sample data for testing
          final sampleLeagues = _createSampleFixtures();
          await saveFixturesToCache(sampleLeagues);
          return sampleLeagues;
        }
      } else {
        if (kDebugMode) {
          print('Fixtures are up to date, loading from cache...');
        }
        return await loadFixturesFromCache();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating fixtures, trying to load from cache: $e');
      }
      final cachedLeagues = await loadFixturesFromCache();
      if (cachedLeagues.isEmpty) {
        // If cache is also empty, return sample data
        return _createSampleFixtures();
      }
      return cachedLeagues;
    }
  }

  static List<FixtureLeague> _createSampleFixtures() {
    return [
      FixtureLeague(
        leagueName: 'Premier League',
        flagClass: 'england',
        leagueUrl: '/competitions/england/premier-league/',
        matches: [
          FixtureMatch(
            matchId: '1001',
            time: '15:00',
            title: 'Manchester United vs Liverpool',
            teams: 'Manchester United vs Liverpool',
            url: '/match/manchester-united-vs-liverpool/test1',
            channels: ['Sky Sports', 'NBC Sports', 'DAZN'],
          ),
          FixtureMatch(
            matchId: '1002',
            time: '17:30',
            title: 'Arsenal vs Chelsea',
            teams: 'Arsenal2 - 1Chelsea',
            url: '/match/arsenal-vs-chelsea/test2',
            channels: ['BT Sport', 'ESPN', 'beIN Sports'],
          ),
        ],
      ),
      FixtureLeague(
        leagueName: 'La Liga',
        flagClass: 'spain',
        leagueUrl: '/competitions/spain/la-liga/',
        matches: [
          FixtureMatch(
            matchId: '2001',
            time: '20:00',
            title: 'Real Madrid vs Barcelona',
            teams: 'Real Madrid vs Barcelona',
            url: '/match/real-madrid-vs-barcelona/test3',
            channels: ['ESPN+', 'Movistar+', 'beIN Sports'],
          ),
        ],
      ),
    ];
  }

  static Future<List<FixtureLeague>> loadFixturesFromCache() async {
    try {
      final file = File(_cacheFilePath);
      if (!await file.exists()) {
        return [];
      }

      final jsonString = await file.readAsString();
      final List<dynamic> jsonData = json.decode(jsonString);

      return jsonData.map((league) => FixtureLeague.fromJson(league)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading fixtures from cache: $e');
      }
      return [];
    }
  }
}
