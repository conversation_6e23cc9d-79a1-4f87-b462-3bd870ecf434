<!DOCTYPE html>
<html lang="en-US">
  <head>
    <title>Just a moment...</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="robots" content="noindex,nofollow" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
      html {
        line-height: 1.15;
        -webkit-text-size-adjust: 100%;
        color: #313131;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI,
          Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif,
          Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
      }
      body {
        display: flex;
        flex-direction: column;
        height: 100vh;
        min-height: 100vh;
      }
      .main-content {
        margin: 8rem auto;
        max-width: 60rem;
        padding-left: 1.5rem;
      }
      @media (width <= 720px) {
        .main-content {
          margin-top: 4rem;
        }
      }
      .h2 {
        font-size: 1.5rem;
        font-weight: 500;
        line-height: 2.25rem;
      }
      @media (width <= 720px) {
        .h2 {
          font-size: 1.25rem;
          line-height: 1.5rem;
        }
      }
      #challenge-error-text {
        background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSI+PHBhdGggZmlsbD0iI0IyMEYwMyIgZD0iTTE2IDNhMTMgMTMgMCAxIDAgMTMgMTNBMTMuMDE1IDEzLjAxNSAwIDAgMCAxNiAzbTAgMjRhMTEgMTEgMCAxIDEgMTEtMTEgMTEuMDEgMTEuMDEgMCAwIDEtMTEgMTEiLz48cGF0aCBmaWxsPSIjQjIwRjAzIiBkPSJNMTcuMDM4IDE4LjYxNUgxNC44N0wxNC41NjMgOS41aDIuNzgzem0tMS4wODQgMS40MjdxLjY2IDAgMS4wNTcuMzg4LjQwNy4zODkuNDA3Ljk5NCAwIC41OTYtLjQwNy45ODQtLjM5Ny4zOS0xLjA1Ny4zODktLjY1IDAtMS4wNTYtLjM4OS0uMzk4LS4zODktLjM5OC0uOTg0IDAtLjU5Ny4zOTgtLjk4NS40MDYtLjM5NyAxLjA1Ni0uMzk3Ii8+PC9zdmc+);
        background-repeat: no-repeat;
        background-size: contain;
        padding-left: 34px;
      }
      @media (prefers-color-scheme: dark) {
        body {
          background-color: #222;
          color: #d9d9d9;
        }
      }
    </style>
    <meta http-equiv="refresh" content="360" />
    <script src="/cdn-cgi/challenge-platform/h/g/orchestrate/chl_page/v1?ray=948555491ea3e22c"></script>
  </head>
  <body>
    <div class="main-wrapper" role="main">
      <div class="main-content">
        <noscript
          ><div class="h2">
            <span id="challenge-error-text"
              >Enable JavaScript and cookies to continue</span
            >
          </div></noscript
        >
      </div>
    </div>
    <script>
      ;(function () {
        window._cf_chl_opt = {
          cvId: '3',
          cZone: 'www.livesoccertv.com',
          cType: 'managed',
          cRay: '948555491ea3e22c',
          cH: 't109vrOAp7Im0.Rjrny7kNmR5Td7RcBMutuA51Z_by8-1748682574-*******-Iat3VIfxI7DhPuJhwuGpI6q8fG_BQsPQCVxpMV.1pBItnz4mig0LAYdjQ.Uc2oGy',
          cUPMDTk:
            '\/schedules\/?__cf_chl_tk=O.KihsO.4tmnVNMSshyoizjl0I6rC8lflBWzvZEZ3dE-1748682574-*******-CrIqb_nWl5S95q9kQs1tYSalodvZsoHTxoezlZR4d68',
          cFPWv: 'g',
          cITimeS: '1748682574',
          cTplC: 0,
          cTplV: 5,
          cTplB: 'cf',
          fa: '\/schedules\/?__cf_chl_f_tk=O.KihsO.4tmnVNMSshyoizjl0I6rC8lflBWzvZEZ3dE-1748682574-*******-CrIqb_nWl5S95q9kQs1tYSalodvZsoHTxoezlZR4d68',
          md: 'j.dU.cBvTN0Q2.uVsBxo.4l7h2ksaZPwilUjndde.CQ-1748682574-*******-H.gOsG8Eh56lWBdBPrcQjhHj5MfPJc0ERJS6gvM44fj6g0TBT0SkIGRqkYWsfsVyD6ygo7t79xMlMmdJk2kny0Ja66cWhW6eF95C83URtmbZ9J8kc.YN2h8DrZzEA_scpVhKB_rdEA_0HywB4hKVz7OaGajUkPoeba28LnqZzu.IoJGNdyqtdkdCObQfErQhCkmkeMMAYr969qlaJRDnuVdUiw4nA6_5lySh.znKJjmoz7yGNIxIrq3uu8rvfknHiolp0ufdvvWx6QPUEWbaOahbmlvmABlmKTx6fwJtF5ljl9XidLMoHL18REIIZ.yk4fYmpIaXK6XHWXzlHy7pPZk10nnWIMiZEU42PBo5piY7M87DFzl8A19acpsliZ2lBcruV7jPcxGjtxwJCOLA6JSRUZbcHT4ljJPoCNKpDM..GmUdeavyn.o91yxLEIj.kkIAX__6imZUNoV5O6Jh4NeGj5neCWvZ7E7Tk.rVA6CkbAnWRMUcq8nsUNe.P2Nrs5Xfatg5RLi5S8.yjnowXbmjx_r4VpfiavOLeaf1m4rYXvR16Z.a2O8vOPjAIA5xHoIGsBMBYHrKQQeSPlTwMtsg0uvWpDgLFIQ7Nood6Ch2ylEALfIOyt4UldcfRbkSpd7fzF03H1gNr6CvSP7MnE.zKUc2U4_7.nCYoLUU4HNSAr8k94r9T0IdBFE1voFWqEdtaG_CjpaEW2lKPua0P4qIhWGnBACgnLzgqdsxARLrg9_JIImDyjuk8lU_6lItf17oYHX4btnGqwTRf2kL1DN2.rNSCCTlXn4D1En7h4IX5FemRaL_gScDEU_IROWedK6Qg5sN4FJsDlZM5opJiCjrSa6kcw2VkCe5SfDZtsAlf1VSfMin668ofk71K9rj0es8Qv41pMNIqg5GCXuA1dcrGcexgV10ZXfZr2.6x3hJqVTPM.s.V3nHdAm4QmoeWDs97priVWbV5XejfwK9g9vGBnBMOfgyCAg0y4jOyjjm4kAh4iKfuVh8I8Es.fIUiLLI_ECBaHPMHaWEHxXMxDi84MuS1RImGCelx6JQcs4',
          mdrd: '.nZxbqUKnp5EzAggK0gdj7c3Aq1nFAfqxm3R4XIvRsk-1748682574-*******-TlF8BblsLoE.pFjlOkScV0ctb_Thnp5y61y1yjyFLNuu945buM3DC2oA.nmG4rRfepn7ThhAdeA2GIxSK7.yMlApNBaQ0XGwZ6oLgNGm6icgdXJDjbDa2TGtmKn4M.5iXyccxVD15Jmw29sOQYvj9SHwH4r0glutJyj8GmHk06DXhPvlUefOpHErEsH7SqTPoxR0dH.6I338RvVbvuXBPCLIJVsPSOPPJkB.coWx5WmxDV9e8zP0GMLE7UGHf37Uii2LlTWmac7mjJX.mTHSjp02ueLIeSVCh0FEG7T8dqIM9rCyRqZ0zon2rLBrVo.b4elwpI5iaUcGshUKJPSnCiJ5dAXUEKqF5x4lSBZABGEfrCKZjPWyUz5qLeMvpo4To_X2u4DILLkdP2L5KaXqfNozLzGp0YdugOWMeQxNx8MMnEhpOC65V1jH7dgB6YY0T9i8GtszTAiPF1PfLzE2ILVEOu1u4hHNIfzr5d6D30WNomS5lbgG.oOrAJKdfRN5gr0dOteE9r2lyN9R9XXONXYvXVmoCjvxailpS9O1WUUbsksQxjuvX3axZt7zX.goqk7iLs7_HXGp5FsIHFFDJL5DJZFvwde48YRQkHYrvrsKE.QmRc9AygCDGnHc0agvDA6gZB5zhYAb438OepyY3PnaaQR0SjUXzXIrNQqIsGjRgLvc3.tLuTCRAZN8Nsc9S2mC_7nWS8wwtOaJB1Ehq4Rj7mKLi7DJGjQBeLt5lldrNerdlBgjrhxK2zQ6PJnlShGxgILnG6VDzaZ1mTNrF5vu5A.4_mehbaKlfKJZfYlnb_Q62FerTlcChLC0FLqdMiZDqwZ8P.45D9p.Nw1IvEL56WNYTB5jp.z9Az.JViqty3YQd_BdjooBeerFjxulKXPxNlW2DU_tzVdz6WAfzsdPU7fGpnLb1e_0ihS0n8w1lH5v_RSU2oLERVr3gTZQBTs2PYSshXeTI_4O8s0rIWvL6xgqWTq6k0ZW20XF7Kf7TxG2qCuqYGg1h0.fva5sdyktiiqgJQ4VgqZLsfxvUV9qScxadEbV4mf4K7f9UNFFy_n44H6VsrQl.SrBjIrsVKYFNbI_lViOAxdVW81G0koFZJvOlKI9KdO0WBqGVe.pSvJd1Hizlwkmrah6kZ301Jupq0cvqYdgi6vKAf2XiYHChL8ZFOtWjGUBzKYY7cPa08ErQqV5n5hjN1vMYYu_YO2JBKeLxij7_wMQmGhoKiJwWzPXc.2DswQDuos0FrKsLoEh_xCijKy7PxZGLp02qQPL.qaJXdPdyEF1TA_KGM8.mscN0CZAxzFiAXVmJAJb5qkDYvNa1hkEbBVLlLoHDuv1VMLluJhsG7Te6iFZVe4a_YUYsT6hmfM.a5G97NM0Bb4nndKA77cLkCk9OI.Cg_goYYdFi1zopuRUuzWJYdMRQf4lFcLDzx21G71_NrA7JKqqkrm9hNKGrY.C8lVLYRfYGw0Vfp58t4gijdkFpA7n1_buBqfNkA3bmj8nvkTV8394tAbuj_P3O0CdANDdJD6yT3FhbUyPS.0EnURU9dCU7T58BLCblqggYM73K4tC7fbKyBebjy5SMrM1kwm3ciawItDiz7n0a5Zhs5eqrvjExr1i5yleMMEoH2vjJhqefYTYjdHtNE_RyOFwZZbonczjrH2kXcWCZhV1YbJp18QdXvu9almDvDRwMZKvSk_8dCsae8HYEWCzou8ne6hds.yD4c.ix0DJHZwhiAkuGeHk5.Y0x5AAC7rOMxsRHwoGu2.0y3gRj_T46BYZFFue6Vd4stQeVJ_5UixgHaZ2yjBYq_KLvJPJE0oRYyjnAz6MNZV33NvIgj6lH5zAN.hr71U4n_BzBRbBwtE4fP0gWjFOGX1q1UHC.KnggCyDXatoB2d.9GbtgR7bXKzW_NEuLCMFkOnUjWmXGQVVYEBVoZOwEVCvWN5V3711wmA25HpYliCQC8WPkMBC4BIl3FFw1Z5JIbaHyHFL_s._1_W0JXEgsznte1oQq687_0jiKjb.uJSVS1Fg8GXkIeCz7Xroo0Jblkbp5OtadqS8pkvDhTX.2Fjo4p40l69d3bslIS8Kj4vIxpphi.FELGqmY3u0YRcKScKp_To5VnbOMDa8utOZW3qjUY0IdviDU3QMchscKbdN8L8jiTBqgZtwihJtoMaaWzzf2.6nVplBPR_WQNMyPiQRzX89j2v3pJjw43wsmQZ2BPacBHcMiOKIhV5n1uPyaBG3f13qfYc.4xYRthlLc51iylEzum0K9wcllRFf8ZxCWBc.bDEHn.XUP6rBLlAi6QJhPBTXvfr80.J_sfbTk.Qnw69scfdDlSLGL.Y',
        }
        var cpo = document.createElement('script')
        cpo.src =
          '/cdn-cgi/challenge-platform/h/g/orchestrate/chl_page/v1?ray=948555491ea3e22c'
        window._cf_chl_opt.cOgUHash =
          location.hash === '' && location.href.indexOf('#') !== -1
            ? '#'
            : location.hash
        window._cf_chl_opt.cOgUQuery =
          location.search === '' &&
          location.href
            .slice(0, location.href.length - window._cf_chl_opt.cOgUHash.length)
            .indexOf('?') !== -1
            ? '?'
            : location.search
        if (window.history && window.history.replaceState) {
          var ogU =
            location.pathname +
            window._cf_chl_opt.cOgUQuery +
            window._cf_chl_opt.cOgUHash
          history.replaceState(
            null,
            null,
            '\/schedules\/?__cf_chl_rt_tk=O.KihsO.4tmnVNMSshyoizjl0I6rC8lflBWzvZEZ3dE-1748682574-*******-CrIqb_nWl5S95q9kQs1tYSalodvZsoHTxoezlZR4d68' +
              window._cf_chl_opt.cOgUHash
          )
          cpo.onload = function () {
            history.replaceState(null, null, ogU)
          }
        }
        document.getElementsByTagName('head')[0].appendChild(cpo)
      })()
    </script>
  </body>
</html>
